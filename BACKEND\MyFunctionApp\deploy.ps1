# Azure Functions Deployment Script for Doc-Scanner (PowerShell)
# Make sure you have Azure CLI and Functions Core Tools installed

param(
    [string]$ResourceGroup = "doc-scanner-rg",
    [string]$Location = "East US",
    [string]$FunctionAppName = "doc-scanner-functions-$(Get-Date -Format 'yyyyMMddHHmmss')"
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting Azure Functions deployment..." -ForegroundColor Green
Write-Host "Resource Group: $ResourceGroup" -ForegroundColor Cyan
Write-Host "Function App: $FunctionAppName" -ForegroundColor Cyan
Write-Host "Location: $Location" -ForegroundColor Cyan

# Check if Azure CLI is installed
try {
    az --version | Out-Null
} catch {
    Write-Host "❌ Azure CLI is not installed. Please install it first." -ForegroundColor Red
    exit 1
}

# Check if Functions Core Tools is installed
try {
    func --version | Out-Null
} catch {
    Write-Host "❌ Azure Functions Core Tools is not installed. Please install it first." -ForegroundColor Red
    exit 1
}

# Login check
Write-Host "🔐 Checking Azure login status..." -ForegroundColor Yellow
try {
    az account show | Out-Null
} catch {
    Write-Host "Please login to Azure first:" -ForegroundColor Yellow
    az login
}

# Generate unique storage account name
$StorageAccount = "docscanstorage$(Get-Date -Format 'yyyyMMddHHmmss')"

try {
    # Create resource group
    Write-Host "📦 Creating resource group..." -ForegroundColor Yellow
    az group create --name $ResourceGroup --location $Location --output table

    # Create storage account
    Write-Host "💾 Creating storage account..." -ForegroundColor Yellow
    az storage account create `
        --name $StorageAccount `
        --resource-group $ResourceGroup `
        --location $Location `
        --sku Standard_LRS `
        --output table

    # Create Function App
    Write-Host "⚡ Creating Function App..." -ForegroundColor Yellow
    az functionapp create `
        --resource-group $ResourceGroup `
        --consumption-plan-location $Location `
        --runtime python `
        --runtime-version 3.11 `
        --functions-version 4 `
        --name $FunctionAppName `
        --storage-account $StorageAccount `
        --os-type Linux `
        --output table

    # Configure application settings
    Write-Host "⚙️ Configuring application settings..." -ForegroundColor Yellow
    az functionapp config appsettings set `
        --name $FunctionAppName `
        --resource-group $ResourceGroup `
        --settings `
            "CORS_ORIGINS=*" `
            "MAX_FILE_SIZE_MB=50" `
            "ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,csv" `
            "LOG_LEVEL=INFO" `
            "PYTHON_ISOLATE_WORKER_DEPENDENCIES=1" `
        --output table

    # Deploy the function
    Write-Host "🚀 Deploying function code..." -ForegroundColor Yellow
    func azure functionapp publish $FunctionAppName --python

    # Get function app details
    Write-Host "📋 Getting deployment details..." -ForegroundColor Yellow
    $FunctionUrl = az functionapp show --name $FunctionAppName --resource-group $ResourceGroup --query "defaultHostName" -o tsv
    $MasterKey = az functionapp keys list --name $FunctionAppName --resource-group $ResourceGroup --query "masterKey" -o tsv

    Write-Host ""
    Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📝 Deployment Details:" -ForegroundColor Cyan
    Write-Host "=====================" -ForegroundColor Cyan
    Write-Host "Function App Name: $FunctionAppName" -ForegroundColor White
    Write-Host "Resource Group: $ResourceGroup" -ForegroundColor White
    Write-Host "Function URL: https://$FunctionUrl" -ForegroundColor White
    Write-Host "Master Key: $MasterKey" -ForegroundColor White
    Write-Host ""
    Write-Host "🔗 API Endpoints:" -ForegroundColor Cyan
    Write-Host "Health Check: https://$FunctionUrl/api/health" -ForegroundColor White
    Write-Host "Image to CSV: https://$FunctionUrl/api/image_to_csv" -ForegroundColor White
    Write-Host "PDF to CSV: https://$FunctionUrl/api/pdf_to_csv" -ForegroundColor White
    Write-Host "Merge CSV: https://$FunctionUrl/api/merge_csv" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 Frontend Configuration:" -ForegroundColor Cyan
    Write-Host "Add these to your frontend .env file:" -ForegroundColor Yellow
    Write-Host "REACT_APP_API_URL=https://$FunctionUrl/api" -ForegroundColor Green
    Write-Host "REACT_APP_BACKEND_URL=https://$FunctionUrl" -ForegroundColor Green
    Write-Host "REACT_APP_AZURE_FUNCTION_KEY=$MasterKey" -ForegroundColor Green
    Write-Host ""
    Write-Host "⚠️ Important Notes:" -ForegroundColor Yellow
    Write-Host "1. Add your Google API keys to the Function App settings" -ForegroundColor White
    Write-Host "2. Test the endpoints before using in production" -ForegroundColor White
    Write-Host "3. Consider using function-specific keys instead of master key" -ForegroundColor White
    Write-Host "4. Set up monitoring and alerts" -ForegroundColor White
    Write-Host ""
    Write-Host "🧪 Test your deployment:" -ForegroundColor Cyan
    Write-Host "curl `"https://$FunctionUrl/api/health?code=$MasterKey`"" -ForegroundColor Green

} catch {
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
