# Azure Functions ignore file
# Files and directories to exclude from deployment

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local development
local.settings.json
.azure/
.git/
.gitignore
README.md

# Test files
tests/
test_*.py
*_test.py

# Documentation
docs/
*.md

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
uploads/
output/
outputs/

# Local server
local_server.py

# Development tools
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# Environment variables
.env.local
.env.development
.env.test
.env.production