#!/bin/bash

# Azure Functions Deployment Script for Doc-<PERSON>anner
# Make sure you have Azure CLI and Functions Core Tools installed

set -e  # Exit on any error

# Configuration
RESOURCE_GROUP="doc-scanner-rg"
LOCATION="East US"
STORAGE_ACCOUNT="docscanstorage$(date +%s)"
FUNCTION_APP="doc-scanner-functions-$(date +%s)"

echo "🚀 Starting Azure Functions deployment..."
echo "Resource Group: $RESOURCE_GROUP"
echo "Function App: $FUNCTION_APP"
echo "Location: $LOCATION"

# Check if Azure CLI is installed
if ! command -v az &> /dev/null; then
    echo "❌ Azure CLI is not installed. Please install it first."
    exit 1
fi

# Check if Functions Core Tools is installed
if ! command -v func &> /dev/null; then
    echo "❌ Azure Functions Core Tools is not installed. Please install it first."
    exit 1
fi

# Login check
echo "🔐 Checking Azure login status..."
if ! az account show &> /dev/null; then
    echo "Please login to Azure first:"
    az login
fi

# Create resource group
echo "📦 Creating resource group..."
az group create --name $RESOURCE_GROUP --location "$LOCATION" --output table

# Create storage account
echo "💾 Creating storage account..."
az storage account create \
  --name $STORAGE_ACCOUNT \
  --resource-group $RESOURCE_GROUP \
  --location "$LOCATION" \
  --sku Standard_LRS \
  --output table

# Create Function App
echo "⚡ Creating Function App..."
az functionapp create \
  --resource-group $RESOURCE_GROUP \
  --consumption-plan-location "$LOCATION" \
  --runtime python \
  --runtime-version 3.11 \
  --functions-version 4 \
  --name $FUNCTION_APP \
  --storage-account $STORAGE_ACCOUNT \
  --os-type Linux \
  --output table

# Configure application settings
echo "⚙️ Configuring application settings..."
az functionapp config appsettings set \
  --name $FUNCTION_APP \
  --resource-group $RESOURCE_GROUP \
  --settings \
    "CORS_ORIGINS=*" \
    "MAX_FILE_SIZE_MB=50" \
    "ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,csv" \
    "LOG_LEVEL=INFO" \
    "PYTHON_ISOLATE_WORKER_DEPENDENCIES=1" \
  --output table

# Deploy the function
echo "🚀 Deploying function code..."
func azure functionapp publish $FUNCTION_APP --python

# Get function app details
echo "📋 Getting deployment details..."
FUNCTION_URL=$(az functionapp show --name $FUNCTION_APP --resource-group $RESOURCE_GROUP --query "defaultHostName" -o tsv)
MASTER_KEY=$(az functionapp keys list --name $FUNCTION_APP --resource-group $RESOURCE_GROUP --query "masterKey" -o tsv)

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📝 Deployment Details:"
echo "====================="
echo "Function App Name: $FUNCTION_APP"
echo "Resource Group: $RESOURCE_GROUP"
echo "Function URL: https://$FUNCTION_URL"
echo "Master Key: $MASTER_KEY"
echo ""
echo "🔗 API Endpoints:"
echo "Health Check: https://$FUNCTION_URL/api/health"
echo "Image to CSV: https://$FUNCTION_URL/api/image_to_csv"
echo "PDF to CSV: https://$FUNCTION_URL/api/pdf_to_csv"
echo "Merge CSV: https://$FUNCTION_URL/api/merge_csv"
echo ""
echo "🔧 Frontend Configuration:"
echo "Add these to your frontend .env file:"
echo "REACT_APP_API_URL=https://$FUNCTION_URL/api"
echo "REACT_APP_BACKEND_URL=https://$FUNCTION_URL"
echo "REACT_APP_AZURE_FUNCTION_KEY=$MASTER_KEY"
echo ""
echo "⚠️ Important Notes:"
echo "1. Add your Google API keys to the Function App settings"
echo "2. Test the endpoints before using in production"
echo "3. Consider using function-specific keys instead of master key"
echo "4. Set up monitoring and alerts"
echo ""
echo "🧪 Test your deployment:"
echo "curl \"https://$FUNCTION_URL/api/health?code=$MASTER_KEY\""
