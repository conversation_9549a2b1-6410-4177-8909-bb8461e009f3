{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "python", "FUNCTIONS_EXTENSION_VERSION": "~4", "PYTHON_ISOLATE_WORKER_DEPENDENCIES": "1", "GOOGLE_API_KEY": "your-google-api-key-here", "GEMINI_API_KEY": "your-gemini-api-key-here", "AZURE_STORAGE_CONNECTION_STRING": "your-azure-storage-connection-string", "AZURE_STORAGE_CONTAINER_NAME": "docscan-files", "CORS_ORIGINS": "*", "MAX_FILE_SIZE_MB": "50", "ALLOWED_FILE_TYPES": "jpg,jpeg,png,pdf,csv", "LOG_LEVEL": "INFO", "DEBUG_MODE": "true"}, "Host": {"CORS": {"SupportCredentials": false, "AllowedOrigins": ["*"], "AllowedMethods": ["GET", "POST", "OPTIONS", "DELETE"], "AllowedHeaders": ["Content-Type", "Authorization", "x-functions-key"]}, "LocalHttpPort": 7071}, "ConnectionStrings": {}}