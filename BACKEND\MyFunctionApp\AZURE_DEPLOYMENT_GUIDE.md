# Azure Functions Deployment Guide

## Prerequisites

1. **Azure CLI** installed and logged in
2. **Azure Functions Core Tools** v4.x
3. **Python 3.11** installed
4. **Azure Subscription** with appropriate permissions

## Step 1: Install Required Tools

```bash
# Install Azure CLI (if not already installed)
# Windows: Download from https://aka.ms/installazurecliwindows
# macOS: brew install azure-cli
# Linux: curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# Install Azure Functions Core Tools
npm install -g azure-functions-core-tools@4 --unsafe-perm true

# Verify installations
az --version
func --version
```

## Step 2: Login to Azure

```bash
# Login to Azure
az login

# Set your subscription (if you have multiple)
az account set --subscription "your-subscription-id"
```

## Step 3: Create Azure Resources

```bash
# Set variables
RESOURCE_GROUP="doc-scanner-rg"
LOCATION="East US"
STORAGE_ACCOUNT="docscanstorage$(date +%s)"
FUNCTION_APP="doc-scanner-functions"

# Create resource group
az group create --name $RESOURCE_GROUP --location "$LOCATION"

# Create storage account
az storage account create \
  --name $STORAGE_ACCOUNT \
  --resource-group $RESOURCE_GROUP \
  --location "$LOCATION" \
  --sku Standard_LRS

# Create Function App
az functionapp create \
  --resource-group $RESOURCE_GROUP \
  --consumption-plan-location "$LOCATION" \
  --runtime python \
  --runtime-version 3.11 \
  --functions-version 4 \
  --name $FUNCTION_APP \
  --storage-account $STORAGE_ACCOUNT \
  --os-type Linux
```

## Step 4: Configure Environment Variables

```bash
# Set application settings
az functionapp config appsettings set \
  --name $FUNCTION_APP \
  --resource-group $RESOURCE_GROUP \
  --settings \
    "GOOGLE_API_KEY=your-google-api-key" \
    "GEMINI_API_KEY=your-gemini-api-key" \
    "CORS_ORIGINS=*" \
    "MAX_FILE_SIZE_MB=50" \
    "ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,csv" \
    "LOG_LEVEL=INFO" \
    "PYTHON_ISOLATE_WORKER_DEPENDENCIES=1"
```

## Step 5: Deploy the Function App

```bash
# Navigate to your function app directory
cd BACKEND/MyFunctionApp

# Deploy to Azure
func azure functionapp publish $FUNCTION_APP --python
```

## Step 6: Get Function URLs and Keys

```bash
# Get the function app URL
FUNCTION_URL=$(az functionapp show --name $FUNCTION_APP --resource-group $RESOURCE_GROUP --query "defaultHostName" -o tsv)
echo "Function App URL: https://$FUNCTION_URL"

# Get the master key (for admin access)
MASTER_KEY=$(az functionapp keys list --name $FUNCTION_APP --resource-group $RESOURCE_GROUP --query "masterKey" -o tsv)
echo "Master Key: $MASTER_KEY"

# Get function-specific keys
az functionapp function keys list --name $FUNCTION_APP --resource-group $RESOURCE_GROUP --function-name "image_to_csv"
```

## Step 7: Update Frontend Configuration

Update your frontend `.env` file with the deployed URLs:

```env
# Azure Production URLs
REACT_APP_API_URL=https://your-function-app.azurewebsites.net/api
REACT_APP_BACKEND_URL=https://your-function-app.azurewebsites.net
REACT_APP_AZURE_FUNCTION_KEY=your-function-key
```

## Step 8: Test Deployment

```bash
# Test health endpoint
curl "https://$FUNCTION_URL/api/health"

# Test with function key
curl "https://$FUNCTION_URL/api/health?code=$MASTER_KEY"
```

## Monitoring and Logs

```bash
# View logs
az functionapp log tail --name $FUNCTION_APP --resource-group $RESOURCE_GROUP

# View metrics
az monitor metrics list --resource "/subscriptions/your-sub-id/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Web/sites/$FUNCTION_APP"
```

## Troubleshooting

### Common Issues:

1. **Deployment Timeout**: Increase timeout in host.json
2. **Package Installation Fails**: Check requirements.txt for compatibility
3. **CORS Issues**: Verify CORS settings in host.json and function code
4. **Memory Issues**: Consider upgrading to Premium plan for large files

### Useful Commands:

```bash
# Restart function app
az functionapp restart --name $FUNCTION_APP --resource-group $RESOURCE_GROUP

# View application settings
az functionapp config appsettings list --name $FUNCTION_APP --resource-group $RESOURCE_GROUP

# Update a setting
az functionapp config appsettings set --name $FUNCTION_APP --resource-group $RESOURCE_GROUP --settings "KEY=VALUE"

# Delete function app (cleanup)
az functionapp delete --name $FUNCTION_APP --resource-group $RESOURCE_GROUP
```

## Security Best Practices

1. **Use Function Keys**: Always use function-specific keys instead of master key
2. **Environment Variables**: Store sensitive data in Application Settings
3. **CORS Configuration**: Restrict CORS origins in production
4. **Authentication**: Consider adding Azure AD authentication
5. **Network Security**: Use VNet integration for enhanced security

## Cost Optimization

1. **Consumption Plan**: Use for variable workloads
2. **Premium Plan**: Use for consistent high-performance needs
3. **Monitor Usage**: Set up billing alerts
4. **Optimize Code**: Reduce cold start times and memory usage

## Next Steps

1. Set up Application Insights for monitoring
2. Configure custom domains
3. Set up CI/CD pipeline
4. Implement proper error handling and logging
5. Add authentication and authorization
